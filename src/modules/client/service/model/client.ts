import { Schema } from "effect";

export const Client = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	fatherName: Schema.NullOr(Schema.String),
	motherName: Schema.NullOr(Schema.String),
	clientType: Schema.String, // "natural" or "juridica"
	documentType: Schema.String,
	document: Schema.String,
	ubication: Schema.NullOr(Schema.String),
	socialReason: Schema.NullOr(Schema.String),
	commercialName: Schema.NullOr(Schema.String),
	condition: Schema.NullOr(Schema.String),
	state: Schema.NullOr(Schema.String),
	hasRetentionRegime: Schema.NullOr(Schema.Boolean),
	businessLineID: Schema.NullOr(Schema.String),
	subBusinessLineID: Schema.NullOr(Schema.String),
	channelID: Schema.NullOr(Schema.String),
	sellerID: Schema.NullOr(Schema.String),
	contactName: Schema.NullOr(Schema.String),
	email: Schema.NullOr(Schema.String),
	phone: Schema.NullOr(Schema.String),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Client = typeof Client.Type;

export const CreateClient = Schema.Struct({
	name: Schema.String,
	fatherName: Schema.optional(Schema.String),
	motherName: Schema.optional(Schema.String),
	clientType: Schema.String,
	documentType: Schema.String,
	document: Schema.String,
	ubication: Schema.optional(Schema.String),
	socialReason: Schema.optional(Schema.String),
	commercialName: Schema.optional(Schema.String),
	condition: Schema.optional(Schema.String),
	state: Schema.optional(Schema.String),
	hasRetentionRegime: Schema.optional(Schema.Boolean),
	businessLineID: Schema.optional(Schema.String),
	subBusinessLineID: Schema.optional(Schema.String),
	channelID: Schema.optional(Schema.String),
	sellerID: Schema.optional(Schema.String),
	contactName: Schema.optional(Schema.String),
	email: Schema.optional(Schema.String),
	phone: Schema.optional(Schema.String),
});
export type CreateClient = typeof CreateClient.Type;

export const UpdateClient = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	fatherName: Schema.optional(Schema.String),
	motherName: Schema.optional(Schema.String),
	clientType: Schema.String,
	documentType: Schema.String,
	document: Schema.String,
	ubication: Schema.optional(Schema.String),
	socialReason: Schema.optional(Schema.String),
	commercialName: Schema.optional(Schema.String),
	condition: Schema.optional(Schema.String),
	state: Schema.optional(Schema.String),
	hasRetentionRegime: Schema.optional(Schema.Boolean),
	businessLineID: Schema.optional(Schema.String),
	subBusinessLineID: Schema.optional(Schema.String),
	channelID: Schema.optional(Schema.String),
	sellerID: Schema.optional(Schema.String),
	contactName: Schema.optional(Schema.String),
	email: Schema.optional(Schema.String),
	phone: Schema.optional(Schema.String),
});
export type UpdateClient = typeof UpdateClient.Type;
