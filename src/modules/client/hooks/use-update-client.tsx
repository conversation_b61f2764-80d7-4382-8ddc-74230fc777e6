import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Client, UpdateClient } from "../service/model/client";
import { clientOptions } from "./client-options";

export default function useUpdateClient() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();
	const queryKey = clientOptions(service).queryKey;

	return useMutation({
		mutationKey: ["update-client"],
		mutationFn: (updateClient: UpdateClient) =>
			AppRuntime.runPromise(client.update(updateClient)),
		onMutate: async (updateClient) => {
			await queryClient.cancelQueries({ queryKey });

			const previousClients = queryClient.getQueryData(queryKey);

			if (previousClients) {
				queryClient.setQueryData(
					queryKey,
					create(previousClients, (draft) => {
						const index = draft.findIndex((c) => c.id === updateClient.id);
						if (index !== -1) {
							draft[index] = {
								...draft[index],
								name: updateClient.name,
								fatherName: updateClient.fatherName || null,
								motherName: updateClient.motherName || null,
								clientType: updateClient.clientType,
								documentType: updateClient.documentType,
								document: updateClient.document,
								ubication: updateClient.ubication || null,
								socialReason: updateClient.socialReason || null,
								commercialName: updateClient.commercialName || null,
								condition: updateClient.condition || null,
								state: updateClient.state || null,
								hasRetentionRegime: updateClient.hasRetentionRegime || null,
								businessLineID: updateClient.businessLineID || null,
								subBusinessLineID: updateClient.subBusinessLineID || null,
								channelID: updateClient.channelID || null,
								sellerID: updateClient.sellerID || null,
								contactName: updateClient.contactName || null,
								email: updateClient.email || null,
								phone: updateClient.phone || null,
								updatedAt: new Date().toISOString(),
							};
						}
					}),
				);
			}

			return { previousClients };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousClients);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
